{<<"app">>,<<"distillery">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"Build releases of your Mix projects with ease!">>}.
{<<"elixir">>,<<"~> 1.6">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/distillery">>,<<"lib/distillery/releases">>,
  <<"lib/distillery/releases/archiver.ex">>,
  <<"lib/distillery/releases/shell.ex">>,
  <<"lib/distillery/releases/checks.ex">>,
  <<"lib/distillery/releases/assembler.ex">>,
  <<"lib/distillery/releases/config">>,
  <<"lib/distillery/releases/config/providers">>,
  <<"lib/distillery/releases/config/providers/elixir.ex">>,
  <<"lib/distillery/releases/config/config.ex">>,
  <<"lib/distillery/releases/config/provider.ex">>,
  <<"lib/distillery/releases/config/load_error.ex">>,
  <<"lib/distillery/releases/plugins">>,
  <<"lib/distillery/releases/plugins/plugin.ex">>,
  <<"lib/distillery/releases/shell">>,
  <<"lib/distillery/releases/shell/macros.ex">>,
  <<"lib/distillery/releases/checks">>,
  <<"lib/distillery/releases/checks/loaded_orphaned_apps.ex">>,
  <<"lib/distillery/releases/checks/cookie.ex">>,
  <<"lib/distillery/releases/checks/erts.ex">>,
  <<"lib/distillery/releases/runtime">>,
  <<"lib/distillery/releases/runtime/control.ex">>,
  <<"lib/distillery/releases/runtime/pidfile.ex">>,
  <<"lib/distillery/releases/archiver">>,
  <<"lib/distillery/releases/archiver/archive.ex">>,
  <<"lib/distillery/releases/models">>,
  <<"lib/distillery/releases/models/profile.ex">>,
  <<"lib/distillery/releases/models/release.ex">>,
  <<"lib/distillery/releases/models/boot_script.ex">>,
  <<"lib/distillery/releases/models/app.ex">>,
  <<"lib/distillery/releases/models/environment.ex">>,
  <<"lib/distillery/releases/errors.ex">>,
  <<"lib/distillery/releases/appups.ex">>,<<"lib/distillery/releases/appup">>,
  <<"lib/distillery/releases/appup/transform_error.ex">>,
  <<"lib/distillery/releases/appup/utils.ex">>,
  <<"lib/distillery/releases/appup/transform.ex">>,
  <<"lib/distillery/releases/overlays.ex">>,
  <<"lib/distillery/releases/utils.ex">>,<<"lib/distillery/tasks">>,
  <<"lib/distillery/tasks/clean.ex">>,<<"lib/distillery/tasks/release.ex">>,
  <<"lib/distillery/tasks/init.ex">>,<<"lib/distillery/tasks/gen.appup.ex">>,
  <<"lib/distillery/cookies.ex">>,<<"priv">>,<<"priv/libexec">>,
  <<"priv/libexec/erts.sh">>,<<"priv/libexec/win">>,
  <<"priv/libexec/win/erts.ps1">>,<<"priv/libexec/win/helpers.ps1">>,
  <<"priv/libexec/win/logger.ps1">>,<<"priv/libexec/win/config.ps1">>,
  <<"priv/libexec/helpers.sh">>,<<"priv/libexec/config.sh">>,
  <<"priv/libexec/env.sh">>,<<"priv/libexec/commands">>,
  <<"priv/libexec/commands/restart.sh">>,<<"priv/libexec/commands/rpc.sh">>,
  <<"priv/libexec/commands/escript.sh">>,
  <<"priv/libexec/commands/unpack.sh">>,<<"priv/libexec/commands/reboot.sh">>,
  <<"priv/libexec/commands/win">>,<<"priv/libexec/commands/win/ping.ps1">>,
  <<"priv/libexec/commands/win/uninstall.ps1">>,
  <<"priv/libexec/commands/win/upgrade.ps1">>,
  <<"priv/libexec/commands/win/console.ps1">>,
  <<"priv/libexec/commands/win/help.ps1">>,
  <<"priv/libexec/commands/win/start.ps1">>,
  <<"priv/libexec/commands/win/escript.ps1">>,
  <<"priv/libexec/commands/win/foreground.ps1">>,
  <<"priv/libexec/commands/win/describe.ps1">>,
  <<"priv/libexec/commands/win/rpc.ps1">>,
  <<"priv/libexec/commands/win/install.ps1">>,
  <<"priv/libexec/commands/win/reboot.ps1">>,
  <<"priv/libexec/commands/win/eval.ps1">>,
  <<"priv/libexec/commands/win/info.ps1">>,
  <<"priv/libexec/commands/win/remote_console.ps1">>,
  <<"priv/libexec/commands/win/stop.ps1">>,
  <<"priv/libexec/commands/win/restart.ps1">>,
  <<"priv/libexec/commands/info.sh">>,<<"priv/libexec/commands/help.sh">>,
  <<"priv/libexec/commands/install.sh">>,<<"priv/libexec/commands/eval.sh">>,
  <<"priv/libexec/commands/ping.sh">>,<<"priv/libexec/commands/console.sh">>,
  <<"priv/libexec/commands/describe.sh">>,
  <<"priv/libexec/commands/pingpeer.sh">>,
  <<"priv/libexec/commands/remote_console.sh">>,
  <<"priv/libexec/commands/command.sh">>,<<"priv/libexec/commands/stop.sh">>,
  <<"priv/libexec/commands/pid.sh">>,<<"priv/libexec/commands/start.sh">>,
  <<"priv/libexec/commands/foreground.sh">>,
  <<"priv/libexec/commands/attach.sh">>,
  <<"priv/libexec/commands/reload_config.sh">>,<<"priv/libexec/readlink.sh">>,
  <<"priv/libexec/logger.sh">>,<<"priv/templates">>,
  <<"priv/templates/release_rc_win_exec.eex">>,
  <<"priv/templates/executable_header.eex">>,
  <<"priv/templates/release_rc_exec.eex">>,
  <<"priv/templates/vm.args.default.eex">>,
  <<"priv/templates/example_config.eex">>,<<"priv/templates/erl_script.eex">>,
  <<"priv/templates/release_rc_entry.eex">>,<<"priv/templates/vm.args.eex">>,
  <<"priv/templates/release_rc_win_main.ps1.eex">>,
  <<"priv/templates/release_rc_main.eex">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE.md">>,<<".formatter.exs">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,
 [{<<"Changelog">>,<<"https://hexdocs.pm/distillery/changelog.html">>},
  {<<"Documentation">>,<<"https://hexdocs.pm/distillery">>},
  {<<"GitHub">>,<<"https://github.com/bitwalker/distillery">>}]}.
{<<"name">>,<<"distillery">>}.
{<<"requirements">>,
 [[{<<"app">>,<<"artificery">>},
   {<<"name">>,<<"artificery">>},
   {<<"optional">>,false},
   {<<"repository">>,<<"hexpm">>},
   {<<"requirement">>,<<"~> 0.2">>}]]}.
{<<"version">>,<<"2.1.1">>}.
