#!/bin/sh

set -e

unset CDPATH

SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd -P)"
REL_NAME="${REL_NAME:-<%= release_name %>}"

IS_TTY=false
if [ -t 1 ]; then
    if command -v tput >/dev/null; then
        IS_TTY=true
    fi
fi
if [ "$IS_TTY" = "true" ]; then
    txtrst=$(tput sgr0 || echo "\e[0m")              # Reset
    txtbld=$(tput bold || echo "\e[1m")              # Bold
    bldred=${txtbld}$(tput setaf 1 || echo "\e[31m") # Red
else
    txtrst=
    txtbld=
    bldred=
fi

if ! command -v bash >/dev/null ; then
    message="Bash is required to run this release. Please ensure that it is installed."
    printf "${bldred}%s${txtrst}\n" "$message"
    exit 1
else
    exec "${SCRIPT_DIR}/${REL_NAME}_rc_exec.sh" "$@"
fi
